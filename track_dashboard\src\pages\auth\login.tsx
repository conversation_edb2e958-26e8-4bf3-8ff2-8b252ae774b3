import { useState } from "react"
import { useNavigate } from "react-router-dom"
import { <PERSON><PERSON><PERSON><PERSON>, FaEyeSlash } from "react-icons/fa"
import { toast } from "sonner"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  <PERSON>,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { loginStrings } from "../../utils/strings/login-strings"
import { LocalStorageService, Keys } from "../../utils/local-storage"
import keys from "../../utils/strings/query-keys"
import authEndpoints from "../../api/service/auth"
import { regex } from "../../utils/strings/auth-strings"
import { useApiMutation } from "../../hooks/react-query-hooks"
import { useAppDispatch } from "../../store/store"
import ICON from "../../assets/icons/icon.png"

type LoginForm = {
  email_address: string
  password: string
}

type LoginErrors = {
  email_address: string
  password: string
}

function Login() {
  const [showPassword, setShowPassword] = useState(false)
  const [form, setForm] = useState<LoginForm>({ email_address: "", password: "" })
  const [error, setError] = useState<LoginErrors>({ email_address: "", password: "" })
  const navigate = useNavigate()
  
  const dispatch = useAppDispatch()

  const { isPending, mutate } = useApiMutation({
    queryKey: [keys.login],
    mutationFn: authEndpoints.login,
    onSuccessHandler: (response: { message: string; email_address: string; refreshToken?: string }) => {
      if (response.message === "OTP Verification Required.") {
        LocalStorageService.setItem(Keys.UserName, response.email_address)
        navigate("/auth/email-verification")
        toast(
           loginStrings.loginSuccessful,{
          description: "OTP verification pending.",
        })
      } else {
        const { email_address, refreshToken } = response
        LocalStorageService.setItem(Keys.Token, refreshToken as string)
        LocalStorageService.setItem(Keys.UserName, email_address)

        navigate("/auth/choose-profile")
        toast("Login successful!", {
  description: "Welcome back!",
})
      }
    },
   onError: (msg: string | null) => {
  toast.error(
     loginStrings.loginFailed,{
    description: msg ?? "Something went wrong",
   
  })
},
  })

  const validateFields = (name: keyof LoginForm, value: string): boolean => {
    let errorMessage = ""
    if (name === "email_address") {
      if (!value.trim()) errorMessage = loginStrings.emailRequired
      else if (!regex.email.test(value)) errorMessage = loginStrings.invalidEmail
    } else if (name === "password") {
      if (!value.trim()) errorMessage = loginStrings.passwordRequired
      else if (!regex.password.test(value)) errorMessage = loginStrings.passwordMinLength
    }
    setError((prev) => ({ ...prev, [name]: errorMessage }))
    return errorMessage === ""
  }

  const validateForm = () => {
    const isEmailValid = validateFields("email_address", form.email_address)
    const isPasswordValid = validateFields("password", form.password)
    return isEmailValid && isPasswordValid
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setForm((prev) => ({ ...prev, [name]: value }))
    validateFields(name as keyof LoginForm, value)
  }

  const handleSubmit = () => {
    if (!validateForm()) return
    mutate(form)
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900 px-4">
      <Card className="w-full max-w-md shadow-lg border border-gray-200 dark:border-gray-700">
        <CardHeader className="flex flex-col items-center space-y-4">
          <img src={ICON} alt="Logo" className="w-16 h-16" />
          <CardTitle className="text-2xl font-semibold text-gray-900 dark:text-white">
            {loginStrings.login}
          </CardTitle>
        </CardHeader>

        <CardContent>
          <Form>
            <div className="space-y-6">
              {/* Email */}
              <FormField name="email_address">
                <FormItem>
                  <FormLabel>{loginStrings.email}</FormLabel>
                  <FormControl>
                    <Input
                      name="email_address"
                      type="email"
                      value={form.email_address}
                      onChange={handleChange}
                      placeholder="Enter your email"
                    />
                  </FormControl>
                  {error.email_address && (
                    <FormMessage>{error.email_address}</FormMessage>
                  )}
                </FormItem>
              </FormField>

              {/* Password */}
              <FormField
  name="password"
  render={({ field }) => (
    <FormItem>
      <FormLabel>{loginStrings.password}</FormLabel>
      <FormControl>
        <Input
          type="password"
          placeholder="Enter your password"
          {...field}   // <-- important: connect RHF
        />
      </FormControl>
      <FormMessage />
    </FormItem>
  )}
/>
            </div>
          </Form>
        </CardContent>

        <CardFooter className="flex flex-col gap-4">
          <Button className="w-full" onClick={handleSubmit} disabled={isPending}>
            {isPending ? "Signing in..." : loginStrings.login}
          </Button>

          <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
            {loginStrings.dontHaveAccount}{" "}
            <button
              onClick={() => navigate("/auth/register")}
              className="text-blue-600 hover:underline"
            >
              {loginStrings.signUp}
            </button>
          </p>
        </CardFooter>
      </Card>
    </div>
  )
}

export default Login
